import MeditationTag from '../models/MeditationTag'
import MeditationContentTag from '../models/MeditationContentTag'
import { Op } from 'sequelize'
import { parsePaginationParams, formatPaginationResponse } from '../tool/Common'

export default class AdminTagController {
  /**
   * @swagger
   * /admin/meditation/tags:
   *   get:
   *     tags:
   *       - 标签管理
   *     summary: 获取标签列表
   *     description: 获取所有冥想标签列表
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: 搜索关键词
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getTagList(ctx) {
    const { search } = ctx.query

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {}

      if (search) {
        whereCondition.name = { [Op.like]: `%${search}%` }
      }

      const tags = await MeditationTag.findAndCountAll({
        where: whereCondition,
        limit: pageSize,
        offset: offset,
        order: [['created_at', 'DESC']],
        include: [
          {
            model: MeditationContent,
            as: 'meditations',
            attributes: [],
            through: { attributes: [] }
          }
        ],
        attributes: [
          'id', 'name', 'created_at',
          [MeditationTag.sequelize.fn('COUNT', MeditationTag.sequelize.col('meditations.id')), 'usage_count']
        ],
        group: ['MeditationTag.id'],
        subQuery: false
      })

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(tags.rows, tags.count.length, pageNum, pageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取标签列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/meditation/tags:
   *   post:
   *     tags:
   *       - 标签管理
   *     summary: 创建标签
   *     description: 创建新的冥想标签
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *             properties:
   *               name:
   *                 type: string
   *                 description: 标签名称
   *     responses:
   *       200:
   *         description: 创建成功
   *       400:
   *         description: 标签已存在
   */
  static async createTag(ctx) {
    const { name } = ctx.request.body

    if (!name || !name.trim()) {
      ctx.body = {
        code: 400,
        message: '标签名称不能为空'
      }
      return
    }

    try {
      // 检查标签是否已存在
      const existingTag = await MeditationTag.findOne({ 
        where: { name: name.trim() } 
      })
      
      if (existingTag) {
        ctx.body = {
          code: 400,
          message: '标签已存在'
        }
        return
      }

      const tag = await MeditationTag.create({
        name: name.trim()
      })

      ctx.body = {
        code: 200,
        message: '标签创建成功',
        data: tag
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '创建标签失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/meditation/tags/{id}:
   *   put:
   *     tags:
   *       - 标签管理
   *     summary: 更新标签
   *     description: 更新指定的标签
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 标签ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *             properties:
   *               name:
   *                 type: string
   *                 description: 标签名称
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 标签不存在
   */
  static async updateTag(ctx) {
    const { id } = ctx.params
    const { name } = ctx.request.body

    if (!name || !name.trim()) {
      ctx.body = {
        code: 400,
        message: '标签名称不能为空'
      }
      return
    }

    try {
      const tag = await MeditationTag.findByPk(id)
      if (!tag) {
        ctx.body = {
          code: 404,
          message: '标签不存在'
        }
        return
      }

      // 检查新名称是否已被其他标签使用
      const existingTag = await MeditationTag.findOne({
        where: { 
          name: name.trim(),
          id: { [Op.ne]: id }
        }
      })

      if (existingTag) {
        ctx.body = {
          code: 400,
          message: '标签名称已被使用'
        }
        return
      }

      await tag.update({ name: name.trim() })

      ctx.body = {
        code: 200,
        message: '标签更新成功',
        data: tag
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新标签失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/meditation/tags/{id}:
   *   delete:
   *     tags:
   *       - 标签管理
   *     summary: 删除标签
   *     description: 删除指定的标签
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 标签ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       404:
   *         description: 标签不存在
   *       400:
   *         description: 标签正在使用中
   */
  static async deleteTag(ctx) {
    const { id } = ctx.params

    try {
      const tag = await MeditationTag.findByPk(id)
      if (!tag) {
        ctx.body = {
          code: 404,
          message: '标签不存在'
        }
        return
      }

      // 检查标签是否正在使用
      const usageCount = await MeditationContentTag.count({
        where: { tag_id: id }
      })

      if (usageCount > 0) {
        ctx.body = {
          code: 400,
          message: `标签正在被 ${usageCount} 个内容使用，无法删除`
        }
        return
      }

      await tag.destroy()

      ctx.body = {
        code: 200,
        message: '标签删除成功'
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除标签失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/meditation/tags/batch-delete:
   *   delete:
   *     tags:
   *       - 标签管理
   *     summary: 批量删除标签
   *     description: 批量删除未使用的标签
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - tag_ids
   *             properties:
   *               tag_ids:
   *                 type: array
   *                 items:
   *                   type: integer
   *                 description: 标签ID数组
   *     responses:
   *       200:
   *         description: 删除成功
   */
  static async batchDeleteTags(ctx) {
    const { tag_ids } = ctx.request.body

    if (!tag_ids || !Array.isArray(tag_ids) || tag_ids.length === 0) {
      ctx.body = {
        code: 400,
        message: '请提供要删除的标签ID数组'
      }
      return
    }

    try {
      // 检查哪些标签正在使用
      const usedTags = await MeditationContentTag.findAll({
        where: { tag_id: { [Op.in]: tag_ids } },
        attributes: ['tag_id'],
        group: ['tag_id']
      })

      const usedTagIds = usedTags.map(item => item.tag_id)
      const canDeleteIds = tag_ids.filter(id => !usedTagIds.includes(id))

      if (canDeleteIds.length === 0) {
        ctx.body = {
          code: 400,
          message: '所选标签都在使用中，无法删除'
        }
        return
      }

      // 删除未使用的标签
      const deletedCount = await MeditationTag.destroy({
        where: { id: { [Op.in]: canDeleteIds } }
      })

      ctx.body = {
        code: 200,
        message: `成功删除 ${deletedCount} 个标签`,
        data: {
          deleted_count: deletedCount,
          used_tag_ids: usedTagIds
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '批量删除标签失败',
        error: error.message
      }
    }
  }
}
